/* Card Product Enhancements - Reviews and Submit Button */

/* Card Reviews Section */
.card__reviews {
  margin: 1rem 0;
  padding: 0.5rem 0;
}

.card__reviews .jdgm-widget {
  margin-bottom: 0.5rem;
}

.card__reviews .shopify-reviews-fallback {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.card__reviews .rating--small {
  font-size: 0.9em;
}

.card__reviews .rating-count {
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.7);
  margin: 0;
}

/* Card Submit Button */
.card__submit-button {
  margin-top: 1rem;
  padding-top: 1rem;
}

.card__submit-btn {
  width: 100%;
  padding: 1rem 1.5rem;
  font-size: 1.4rem;
  font-weight: 500;
  border-radius: var(--buttons-radius);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.card__submit-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(var(--color-shadow), 0.2);
}

.card__submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.card__submit-btn .loading-spinner {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
}

/* Responsive adjustments */
@media screen and (max-width: 749px) {
  .card__reviews {
    margin: 0.8rem 0;
  }
  
  .card__submit-btn {
    padding: 0.8rem 1.2rem;
    font-size: 1.3rem;
  }
}

/* Card layout improvements */
.card-wrapper .card__content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-wrapper .card__information {
  flex-grow: 1;
}

.card-wrapper .card__submit-button {
  margin-top: auto;
}

/* Judge.me widget styling */
.jdgm-widget.jdgm-preview-badge {
  display: inline-block;
  margin: 0;
}

.jdgm-widget .jdgm-star {
  color: #ffc107;
}

.jdgm-widget .jdgm-rev-widg__summary-text {
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.7);
}

/* Hide Shopify reviews when Judge.me is active */
.jdgm-widget:not(:empty) + .shopify-reviews-fallback {
  display: none;
}

/* Animation for submit button */
@keyframes submitSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

.card__submit-btn.success {
  animation: submitSuccess 0.3s ease;
}

/* Loading state */
.card__submit-btn[aria-busy="true"] {
  pointer-events: none;
}

.card__submit-btn[aria-busy="true"] .loading-spinner {
  display: block;
}

.card__submit-btn:not([aria-busy="true"]) .loading-spinner {
  display: none;
}

/* Sold out styling */
.card__submit-btn:disabled {
  background-color: rgba(var(--color-foreground), 0.1);
  color: rgba(var(--color-foreground), 0.5);
  border-color: rgba(var(--color-foreground), 0.1);
}

/* Focus states for accessibility */
.card__submit-btn:focus-visible {
  outline: 2px solid rgb(var(--color-foreground));
  outline-offset: 2px;
}

.jdgm-widget:focus-visible {
  outline: 2px solid rgb(var(--color-foreground));
  outline-offset: 2px;
  border-radius: 4px;
}
