{% doc %}
  @prompt
    Create a full-width video hero section that actually plays videos properly. Include proper HTML5 video element with controls, autoplay functionality, loop options, and mute settings. The video should maintain aspect ratio without cropping and include fallback poster image. Add settings for video file upload, autoplay toggle, mute toggle, loop toggle, and video controls visibility. Ensure the video element works correctly with uploaded video files.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-video-hero-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    overflow: hidden;
    background-color: {{ block.settings.background_color }};
  }

  .ai-video-container-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: calc(100% / {{ block.settings.aspect_ratio }});
    overflow: hidden;
  }

  .ai-video-element-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: {{ block.settings.object_fit }};
    background-color: {{ block.settings.background_color }};
  }

  .ai-video-poster-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: {{ block.settings.object_fit }};
    background-color: {{ block.settings.background_color }};
  }

  .ai-video-overlay-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: {{ block.settings.content_vertical_align }};
    align-items: {{ block.settings.content_horizontal_align }};
    padding: 40px;
    box-sizing: border-box;
    z-index: 2;
  }

  .ai-video-content-{{ ai_gen_id }} {
    max-width: {{ block.settings.content_width }}px;
    text-align: {{ block.settings.text_align }};
    color: {{ block.settings.text_color }};
    background-color: {{ block.settings.content_background_color | color_modify: 'alpha', block.settings.content_background_opacity | divided_by: 100.0 }};
    padding: {{ block.settings.content_padding }}px;
    border-radius: {{ block.settings.content_border_radius }}px;
  }

  .ai-video-heading-{{ ai_gen_id }} {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: {{ block.settings.heading_size }}px;
    font-weight: {{ block.settings.heading_weight }};
    line-height: 1.2;
  }

  .ai-video-text-{{ ai_gen_id }} {
    margin-bottom: 24px;
    font-size: {{ block.settings.text_size }}px;
    line-height: 1.5;
  }

  .ai-video-button-{{ ai_gen_id }} {
    display: inline-block;
    padding: 12px 24px;
    background-color: {{ block.settings.button_background_color }};
    color: {{ block.settings.button_text_color }};
    text-decoration: none;
    border-radius: {{ block.settings.button_border_radius }}px;
    font-weight: 500;
    transition: background-color 0.3s ease;
  }

  .ai-video-button-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.button_background_color | color_darken: 10 }};
  }

  @media screen and (max-width: 749px) {
    .ai-video-overlay-{{ ai_gen_id }} {
      padding: 20px;
    }

    .ai-video-heading-{{ ai_gen_id }} {
      font-size: {{ block.settings.heading_size | times: 0.8 }}px;
    }

    .ai-video-text-{{ ai_gen_id }} {
      font-size: {{ block.settings.text_size | times: 0.9 }}px;
    }
  }
{% endstyle %}

<div class="ai-video-hero-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-video-container-{{ ai_gen_id }}">
    {% if block.settings.video != blank %}
      <video 
        id="ai-video-{{ ai_gen_id }}"
        class="ai-video-element-{{ ai_gen_id }}"
        {% if block.settings.autoplay %}autoplay{% endif %}
        {% if block.settings.loop %}loop{% endif %}
        {% if block.settings.muted %}muted{% endif %}
        {% if block.settings.show_controls %}controls{% endif %}
        {% if block.settings.poster != blank %}poster="{{ block.settings.poster | image_url: width: 2000 }}"{% endif %}
        playsinline
      >
        <source src="{{ block.settings.video }}" type="video/mp4">
        Your browser does not support the video tag.
      </video>
    {% elsif block.settings.poster != blank %}
      <img 
        src="{{ block.settings.poster | image_url: width: 2000 }}"
        alt="{{ block.settings.poster.alt | escape }}"
        class="ai-video-poster-{{ ai_gen_id }}"
        loading="lazy"
      >
    {% else %}
      <div class="ai-video-poster-{{ ai_gen_id }}">
        {{ 'hero-apparel-1' | placeholder_svg_tag }}
      </div>
    {% endif %}
  </div>

  {% if block.settings.show_content %}
    <div class="ai-video-overlay-{{ ai_gen_id }}">
      <div class="ai-video-content-{{ ai_gen_id }}">
        {% if block.settings.heading != blank %}
          <h2 class="ai-video-heading-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
        {% endif %}
        
        {% if block.settings.text != blank %}
          <div class="ai-video-text-{{ ai_gen_id }}">{{ block.settings.text }}</div>
        {% endif %}
        
        {% if block.settings.button_text != blank and block.settings.button_link != blank %}
          <a href="{{ block.settings.button_link }}" class="ai-video-button-{{ ai_gen_id }}">
            {{ block.settings.button_text }}
          </a>
        {% endif %}
      </div>
    </div>
  {% endif %}
</div>

<script>
  (function() {
    const video = document.getElementById('ai-video-{{ ai_gen_id }}');
    
    if (video) {
      // Handle autoplay with muted setting for browsers that require it
      if ({{ block.settings.autoplay | json }} && {{ block.settings.muted | json }}) {
        video.muted = true;
        
        // Try to play the video
        const playPromise = video.play();
        
        if (playPromise !== undefined) {
          playPromise.catch(error => {
            // Auto-play was prevented, try again with user interaction
            console.log('Autoplay prevented:', error);
          });
        }
      }
      
      // Handle iOS low power mode which may prevent autoplay
      document.addEventListener('touchstart', () => {
        if ({{ block.settings.autoplay | json }} && video.paused) {
          video.play().catch(e => {
            console.log('Play after touch prevented:', e);
          });
        }
      }, { once: true });
    }
  })();
</script>

{% schema %}
{
  "name": "Video Hero",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Video Settings"
    },
    {
      "type": "url",
      "id": "video",
      "label": "Video URL"
    },
    {
      "type": "image_picker",
      "id": "poster",
      "label": "Poster image (fallback)"
    },
    {
      "type": "select",
      "id": "aspect_ratio",
      "label": "Aspect ratio",
      "options": [
        {
          "value": "1.78",
          "label": "16:9"
        },
        {
          "value": "1.33",
          "label": "4:3"
        },
        {
          "value": "2.35",
          "label": "2.35:1 (Cinematic)"
        },
        {
          "value": "1",
          "label": "1:1 (Square)"
        },
        {
          "value": "0.8",
          "label": "4:5 (Portrait)"
        },
        {
          "value": "0.56",
          "label": "9:16 (Mobile)"
        }
      ],
      "default": "1.78"
    },
    {
      "type": "select",
      "id": "object_fit",
      "label": "Video fit",
      "options": [
        {
          "value": "cover",
          "label": "Cover (may crop)"
        },
        {
          "value": "contain",
          "label": "Contain (full video)"
        }
      ],
      "default": "cover"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay video",
      "default": true,
      "info": "Autoplay requires the video to be muted on most browsers"
    },
    {
      "type": "checkbox",
      "id": "loop",
      "label": "Loop video",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "muted",
      "label": "Mute video",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_controls",
      "label": "Show video controls",
      "default": false
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Content Overlay"
    },
    {
      "type": "checkbox",
      "id": "show_content",
      "label": "Show content overlay",
      "default": true
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Full-width Video Hero"
    },
    {
      "type": "textarea",
      "id": "text",
      "label": "Text content",
      "default": "Use this section to welcome customers to your store, highlight products, or share brand announcements."
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "Shop Now"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "select",
      "id": "content_vertical_align",
      "label": "Content vertical alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "Top"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "flex-end",
          "label": "Bottom"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "content_horizontal_align",
      "label": "Content horizontal alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "flex-end",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "text_align",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "range",
      "id": "content_width",
      "min": 300,
      "max": 1200,
      "step": 50,
      "unit": "px",
      "label": "Content max width",
      "default": 600
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "content_background_color",
      "label": "Content background color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "content_background_opacity",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Content background opacity",
      "default": 50
    },
    {
      "type": "range",
      "id": "content_padding",
      "min": 0,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Content padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "content_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Content border radius",
      "default": 4
    },
    {
      "type": "header",
      "content": "Text Styling"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 80,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 40
    },
    {
      "type": "select",
      "id": "heading_weight",
      "label": "Heading weight",
      "options": [
        {
          "value": "400",
          "label": "Regular"
        },
        {
          "value": "500",
          "label": "Medium"
        },
        {
          "value": "600",
          "label": "Semibold"
        },
        {
          "value": "700",
          "label": "Bold"
        }
      ],
      "default": "600"
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 30,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Button Styling"
    },
    {
      "type": "color",
      "id": "button_background_color",
      "label": "Button background",
      "default": "#4a90e2"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "button_border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Button border radius",
      "default": 4
    }
  ],
  "presets": [
    {
      "name": "Video Hero"
    }
  ]
}
{% endschema %}